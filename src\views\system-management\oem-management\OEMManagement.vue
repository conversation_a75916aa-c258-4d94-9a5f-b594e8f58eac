<template>
    <div style="background-color: #f7f7f7;">
        <div style="margin-bottom: 16px; background-color: #fff; padding: 16px">
            <searchBox
                :searchOptionKey="'OEM_MANAGEMENT_SEARCH_OPTIONS'"
                @updateSearchParams="updateSearchParams"
            >
            </searchBox>
        </div>
        <div style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="display-flex justify-flex-end font-16 b-margin-16">
                <!-- 新增按钮 -->
                <el-button type="primary" @click="handleAdd">新增</el-button>
            </div>
            <el-table
                :data="OEMList"
                style="width: 100%;height: 500px;"
                empty-text="暂无数据"
                v-loading="tableLoading"
                show-overflow-tooltip
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="oem-key" prop="key" min-width="150"></el-table-column>
                <el-table-column label="底部时间" width="160">
                    <template #default="scope">
                        {{ scope.row.platform.cDate }}
                    </template>
                </el-table-column>
                <el-table-column label="底部公司名" prop="login_CompanyName" width="170">
                    <template #default="scope">
                        {{ scope.row.platform.cName }}
                    </template>
                </el-table-column>
                <el-table-column label="左侧顶部文字" width="170">
                    <template #default="scope">
                        {{ scope.row.platform.companyName }}
                    </template>
                </el-table-column>
                <el-table-column label="左上角logo" width="160">
                    <template #default="scope">
                        <div v-if="scope.row.platform.loginLogo" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.platform.loginLogo" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.platform.loginLogo]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template>                    
                </el-table-column>
                <el-table-column label="登录页图片" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.platform.logoImg" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.platform.logoImg" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.platform.logoImg]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template>   
                </el-table-column>
                <el-table-column label="网页网签标题" width="150">
                    <template #default="scope">
                        {{ scope.row.platform.webPageTabTitle }}
                    </template>
                </el-table-column>
                <el-table-column label="底部备案号" width="150">
                    <template #default="scope">
                        {{ scope.row.platform.tcp }}
                    </template>
                </el-table-column>
                <el-table-column label="首页上方的logo" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.platform.logoImg" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.platform.logoImg" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.platform.logoImg]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template>   
                </el-table-column>
                <el-table-column label="帮助手册地址" width="150">
                    <template #default="scope">
                        {{ scope.row.platform.helpManualAddress }}
                    </template>
                </el-table-column>
                <el-table-column label="首页logo" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.cloudService.mpLogo" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.cloudService.mpLogo" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.cloudService.mpLogo]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template>   
                </el-table-column>
                <el-table-column label="首页顶部图" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.cloudService.mpLogo" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.cloudService.mpLogo" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.cloudService.mpLogo]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template> 
                </el-table-column>
                <el-table-column label="首页搜索下方图" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.cloudService.mpHomeSpec" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.cloudService.mpHomeSpec" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.cloudService.mpHomeSpec]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template> 
                </el-table-column>
                <el-table-column label="是否开启精简授权" width="160">
                    <template #default="scope">
                        <div v-if="scope.row.cloudService.easyCollect === 0">未开启</div>
                        <div v-else>已开启</div>
                    </template>
                </el-table-column>
                <el-table-column label="是否开启票易融" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.cloudService.pyr === 0">未开启</div>
                        <div v-else>已开启</div>
                    </template>
                </el-table-column>
                <el-table-column label="业务进件二维码海报图" width="200">
                    <template #default="scope">
                        <div v-if="scope.row.cloudService.mpQrCode" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.cloudService.mpQrCode" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.cloudService.mpQrCode]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template> 
                </el-table-column>
                <el-table-column label="业务进件分享图" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.cloudService.qrCodeImg" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.cloudService.qrCodeImg" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.cloudService.qrCodeImg]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template> 
                </el-table-column>
                <el-table-column label="业务进件二维码信息" width="180">
                    <template #default="scope">
                        {{ scope.row.cloudService.mpQrcodeText }}
                    </template> 
                </el-table-column>
                <el-table-column label="高企报告封面" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.report.gqbgCover" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.report.gqbgCover" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.report.gqbgCover]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template> 
                </el-table-column>
                <el-table-column label="税务报告封面" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.report.swbgCover" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.report.swbgCover" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.report.swbgCover]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template> 
                </el-table-column>
                <el-table-column label="发票报告封面" width="150">
                    <template #default="scope">
                        <div v-if="scope.row.report.fpbgCover" class="demo-image">
                            <div class="block">
                                <el-image 
                                    style="width: 100px; height: 100px" 
                                    :src="scope.row.report.fpbgCover" 
                                    fit="fill" 
                                    :preview-src-list="[scope.row.report.fpbgCover]" 
                                    :preview-teleported="true" 
                                />
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template> 
                </el-table-column>
                <el-table-column label="数据大屏标题" width="150">
                    <template #default="scope">
                        {{ scope.row.other.dashboardTitle }}
                    </template>
                </el-table-column>
                <el-table-column label="授权协议" width="150">
                    <template #default="scope">
                        {{ scope.row.other.sqxy }}
                    </template>
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="150px"
                >
                    <template #default="scope" >
                        <div class="display-flex gap-10">
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="editOEM(scope.row)"
                            >
                                编辑
                            </div>
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="deleteOEM(scope.row)"
                            >
                                删除
                            </div>
                        </div>

                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-affix v-show="!tableLoading" position="bottom" :offset="16">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
    </div>
    <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="60%"
        @close="dialogVisible = false"
    >   
        <AddOEM v-if="addDialogVisible" @cancel="handleCancel" ></AddOEM>
        <EditOEM v-if="editDialogVisible" :addData="currentRow" @cancel="handleCancel"></EditOEM>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddOEM from './components/addOEM.vue'
import EditOEM from './components/editOEM.vue'
import searchBox from '@/components/common/SearchBox.vue'
import type { IOEMListParams, IOEMListResponseItem } from '@/types/OEM'
import systemService from '@/service/systemService'

let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

let queryParams = reactive<IOEMListParams>({
    page: 1,
    pageSize: 20,
})
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    getOEMList()
}

const getOEMList = () => {
    tableLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    systemService.systemOemList(queryParams).then((res) => {
        if(res.success){
            console.log(res)
            OEMList.value = res.data
            pageInfo.total = res.total
        }else(
            ElMessage.error('系统错误')
        )
    }).then(() => {
        tableLoading.value = false
    })
}

const updateSearchParams = () => {
    getOEMList()
}

const handleAdd = () => {
    dialogTitle.value = '新增'
    addDialogVisible.value = true
    editDialogVisible.value = false
    dialogVisible.value = true
}

const editOEM = (row:IOEMListResponseItem) => {
    console.log(row)
    dialogTitle.value = '编辑'
    addDialogVisible.value = false
    editDialogVisible.value = true
    dialogVisible.value = true
}

const deleteRow = (row:IOEMListResponseItem) => {
    systemService.systemOEMRemove({id:row.id}).then((res) => {
        if(res.success){
            ElMessage.success('删除成功')
            getOEMList()
        }else{
            ElMessage.error(res.errMsg)
        }
    })
}
const deleteOEM = (row:IOEMListResponseItem) => {
    console.log(row)
    ElMessageBox.confirm('是否删除该企业OEM配置', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        deleteRow(row)
    })
}

const tableLoading = ref(false)
const OEMList = ref<IOEMListResponseItem[]>([])

const dialogTitle = ref('')
const dialogVisible = ref(false)
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentRow = ref<IOEMListResponseItem>()
const handleCancel = () => {
    dialogVisible.value = false
}

onMounted(() => {
    getOEMList()
})
</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';


.demo-image .block {
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  display: inline-block;
  width: 20%;
  box-sizing: border-box;
  vertical-align: top;
}
.demo-image .block:last-child {
  border-right: none;
}
.demo-image .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>